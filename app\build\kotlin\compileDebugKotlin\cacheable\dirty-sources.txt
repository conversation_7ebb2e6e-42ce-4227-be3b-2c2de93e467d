E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\CompletionRepository.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\ui\details\HabitDetailsScreen.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\uhabits_99\ui\theme\Color.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\ui\createmeasurablehabit\CreateMeasurableHabitScreen.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\ui\auth\AuthScreen.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\uhabits_99\ui\theme\Type.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\ui\auth\VerificationScreen.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\ui\createyesnohabit\CreateYesNoHabitScreen.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\firestore\FirestoreCompletion.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\HabitSection.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\uhabits_99\MainActivity.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\Habit.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\firestore\FirestoreConverters.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\firestore\FirestoreHabitSection.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\firestore\FirestoreHabit.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\ui\auth\AuthViewModel.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\ui\managesections\ManageSectionsViewModel.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\UserPreferencesRepository.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\ui\habittypeselection\HabitTypeSelectionScreen.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\utils\HabitScheduler.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\HabitType.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\Completion.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\ui\home\HomeScreen.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\ui\MainViewModel.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\ui\components\NumericalInputDialog.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\ui\createhabit\CreateHabitViewModel.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\ui\managesections\ManageSectionsScreen.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\di\DatabaseModule.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\ui\components\EnhancedFrequencyPickerDialog.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\HabitSectionRepository.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\HabitRepository.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\FrequencyModels.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\ui\settings\SettingsScreen.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\HabitsApplication.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\ui\settings\SettingsViewModel.kt
E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\uhabits_99\ui\theme\Theme.kt