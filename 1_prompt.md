# 📝 Prompt: Migrate Data Layer from Room to Firestore

## 1. Objective

To completely replace the local Room database with Cloud Firestore as the app's data persistence layer. This involves refactoring all repository classes to perform CRUD (Create, Read, Update, Delete) operations on Firestore instead of the local Room DAOs, ensuring all user data is stored online and associated with their authenticated account.

## 2. Detailed Implementation Plan

This migration requires a new data structure in Firestore and a complete rewrite of the data access logic in the repository layer.

### Task 2.1: Define Firestore Data Structure

* **Goal:** Establish a secure and scalable data model within Firestore.
* **Action:** All user-generated data (habits, completions, etc.) must be stored under a top-level collection named `users`. Each user's data will be stored in sub-collections within their own document. This ensures that user data is isolated.
    * **Habits Collection:** A user's habits will be stored at: `users/{userId}/habits/{habitId}`
    * **Completions Collection:** A user's completions will be stored at: `users/{userId}/completions/{completionId}`
    * **Data Models:** You may need to create new, simple data classes (POJOs/POCOs) that mirror the existing Room entities but without any Room-specific annotations (`@Entity`, `@PrimaryKey`, etc.). These will be used for serialization/deserialization with Firestore.

### Task 2.2: Refactor Repository Classes

* **Goal:** Replace all Room DAO calls with Firestore SDK calls, making sure every operation is tied to the currently logged-in user.
* **Action:** Go through every repository in the project (e.g., `HabitRepository`, `CompletionRepository`, etc.) and refactor each method.

    * **Get Current User ID:** In each repository function, the first step must be to get the current user's ID: `val userId = Firebase.auth.currentUser?.uid`. If the ID is null, the operation should fail gracefully.

    * **Read Operations (e.g., `getHabits`):**
        * Replace calls to `habitDao.getAll()` with Firestore queries.
        * **Use real-time listeners (`.addSnapshotListener()`)** instead of one-time `get()` calls. This is crucial for a modern, reactive UI. The listener will provide a stream of data that can be observed by the ViewModel.
        * The Firestore query **MUST** target the user-specific collection and filter by their ID. For example: `firestore.collection("users").document(userId).collection("habits").addSnapshotListener(...)`

    * **Write Operations (e.g., `insertHabit`):**
        * Replace `habitDao.insert()` with `firestore.collection("users").document(userId).collection("habits").add(habitObject)`.
        * Ensure the `habitObject` is one of the simple data classes defined in Task 2.1.

    * **Update Operations (e.g., `updateHabit`):**
        * Replace `habitDao.update()` with `firestore.collection("users").document(userId).collection("habits").document(habitId).set(habitObject)` or `.update(...)`.

    * **Delete Operations (e.g., `deleteHabit`):**
        * Replace `habitDao.delete()` with `firestore.collection("users").document(userId).collection("habits").document(habitId).delete()`.

### Task 2.3: Remove Room Database Implementation

* **Goal:** Once all data operations are handled by Firestore, completely remove the now-obsolete Room database code.
* **Action:**
    * Delete the `HabitDatabase.kt` file (the `@Database` class).
    * Delete all DAO interface files (`HabitDao.kt`, `CompletionDao.kt`, etc.).
    * Remove all Room-related dependencies from the `build.gradle.kts` file.
    * Remove any Hilt/Dagger modules related to providing the Room database and DAOs.

## 3. Verification / Testing Section

* **Test Case 1 (Data Creation):**
    * Perform a fresh install of the app.
    * Create a new user account.
    * Create a new "Yes/No" habit and a new "Measurable" habit.
    * **Expected Outcome:** Go to the Firebase Console. Under Firestore, verify that a document exists at `users/{your_new_userId}` and that it contains a `habits` sub-collection with two new documents corresponding to the habits you just created.

* **Test Case 2 (Data Persistence & Reading):**
    * After creating the habits in the test above, completely close and restart the app.
    * **Expected Outcome:** The app should automatically log you in, and the two habits you created must be displayed on the home screen, proving they were fetched correctly from Firestore.

* **Test Case 3 (Data Updates):**
    * Mark a habit as complete for today.
    * **Expected Outcome:** Check the Firestore console. Verify that a new document has been created in the `users/{userId}/completions` sub-collection for that habit and date. The UI on the home screen should update in real-time.

* **Test Case 4 (Critical - Data Isolation):**
    * Log out of your current user account.
    * Create a **second, different user account** (User B).
    * Log in as User B.
    * **Expected Outcome:** The home screen must be empty. User B should **NOT** see any of the habits created by the first user. This confirms that your Firestore queries are correctly and securely filtered by `userId`.

## 4. Mandatory Development Guidelines

**These practices must be followed during all phases of development—planning, implementation, and review.**

### 1. Refer to the Style Guide
Before starting any feature:
- Always consult the **style guide** for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns.
- The style guide is located in the **`style.md` file in the root folder`**.
- It is the **single source of truth** for styling decisions.

### 2. Study the Reference Project
Prior to implementation:
- Review the **reference project** located in the `uhabits-dev` folder (in the root directory).
- Understand how similar features have been approached to maintain consistency and avoid duplications or contradictions.
- The reference project serves as a **blueprint** for implementation.
- This step is mandatory. **Do not proceed to implementation without this step.**

### 3. Understand the Existing Project Structure
Before writing any code:
- Spend time exploring and understanding how the current system is structured.
- Even for new features, existing components or utility functions may be reusable.
- Integrate changes **cleanly into the existing architecture** instead of creating disconnected code.

### 4. Maintain a Clean Codebase
After implementing features:
- Remove any temporary, test, or duplicate files, folders, routes, or unused components that were created during development.
- Keep the codebase **organized and clutter-free**.

### 5. Pause If There Is Any Confusion
If at any point the requirements are unclear:
- **Do not proceed** based on assumptions.
- Immediately pause and seek clarification either from the project lead or directly from me.
- It is better to get clarity than to redo or fix avoidable mistakes later.

### 6. Remove Unused Old Implementations
As part of final review:
- Identify and delete **any old, unused code** that was implemented earlier but is no longer in use.
- This includes obsolete routes, modules, features, or legacy logic.